# 跨平台表格截图工具

## 简介
这是一个跨平台的Excel/WPS表格截图工具，支持Windows、macOS和Linux系统。提供两个版本：
- `excel_screenshot_hotkey.py` - 无界面热键版本（推荐）
- `excel_to_image.py` - 带GUI界面版本

## 依赖安装

### Windows系统
```bash
pip install pillow keyboard
```

### macOS/Linux系统
```bash
pip install pillow pynput
```

### 系统特定依赖
- **Linux**: 需要安装 `sudo apt-get install xclip`
- **macOS**: 需要安装 `brew install pngpaste`

## 使用方法

### 无界面热键版本（推荐）
```bash
python excel_screenshot_hotkey.py
```

**热键说明：**
- `Ctrl+Alt+1`: 从剪贴板获取图片并保存
- `Ctrl+Alt+2`: 手动截图并保存  
- `Ctrl+Alt+Q`: 退出程序

**使用步骤：**
1. 在WPS/Excel中选择表格区域
2. 右键选择"复制为图片"
3. 按 `Ctrl+Alt+1` 保存图片
4. 或者按 `Ctrl+Alt+2` 进行手动截图

### GUI界面版本
```bash
python excel_to_image.py
```

**功能特点：**
- 剪贴板图片方式（推荐）
- COM接口截图方式（仅Windows）
- 手动截图方式
- 支持PNG、JPG、PDF格式
- 实时预览功能

## 技术特点
- 跨平台兼容（Windows/macOS/Linux）
- 保持原始表格样式（所见即所得）
- 支持中文字体自动检测
- 智能编码转换
- 热键快速操作

## 常见问题

**Q: 中文显示异常？**
A: 程序会自动检测系统中文字体，重启程序即可。

**Q: 热键不响应？**
A: 确保安装了对应的热键库，Linux系统可能需要root权限。

**Q: 剪贴板获取失败？**
A: 确保在表格软件中使用"复制为图片"功能，而不是普通复制。

## 文件说明
- `excel_screenshot_hotkey.py` - 无界面热键版本（跨平台）
- `excel_to_image.py` - GUI界面版本（功能完整）
- `README.md` - 本说明文件

---
图片默认保存到桌面，支持自定义保存位置。
