#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格区域导出为图片工具 - Windows版本
直接从Excel文件中读取指定区域并导出为图片

依赖安装：
pip install pandas openpyxl pillow

使用方法：
export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path)
"""

import pandas as pd
import os
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import openpyxl
from openpyxl.utils import range_boundaries


def get_chinese_font(font_size=12):
    """获取支持中文的字体"""
    chinese_fonts = [
        "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
        "C:/Windows/Fonts/simhei.ttf",      # 黑体
        "C:/Windows/Fonts/simsun.ttc",      # 宋体
        "C:/Windows/Fonts/simkai.ttf",      # 楷体
        "arial.ttf"                         # 备用英文字体
    ]

    for font_path in chinese_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            return font
        except (OSError, IOError):
            continue

    # 如果所有字体都加载失败，使用默认字体
    try:
        return ImageFont.load_default()
    except:
        return None


def parse_area_string(area_str):
    """
    解析区域字符串，支持多种格式
    :param area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)" 或 "A1,C5"
    :return: (start_col, start_row, end_col, end_row)
    """
    # 清理字符串，移除括号和空格
    area_str = area_str.strip().replace('(', '').replace(')', '')

    if ':' in area_str:
        # Excel标准格式 "A1:C5"
        start_cell, end_cell = area_str.split(':')
    elif ',' in area_str:
        # 逗号分隔格式 "A1,C5"
        start_cell, end_cell = area_str.split(',')
    else:
        raise ValueError(f"无法解析区域字符串: {area_str}")

    # 使用openpyxl解析单元格坐标，兼容不同版本
    try:
        # 新版本openpyxl
        from openpyxl.utils.cell import coordinate_from_string, column_index_from_string
    except ImportError:
        try:
            # 旧版本openpyxl
            from openpyxl.utils import coordinate_from_string, column_index_from_string
        except ImportError:
            # 如果都导入失败，使用自定义解析函数
            return parse_cell_coordinate_manual(start_cell.strip(), end_cell.strip())

    start_col_letter, start_row = coordinate_from_string(start_cell.strip())
    end_col_letter, end_row = coordinate_from_string(end_cell.strip())

    start_col = column_index_from_string(start_col_letter) - 1  # 转换为0基索引
    end_col = column_index_from_string(end_col_letter) - 1
    start_row = start_row - 1  # 转换为0基索引
    end_row = end_row - 1

    return start_col, start_row, end_col, end_row


def parse_cell_coordinate_manual(start_cell, end_cell):
    """
    手动解析单元格坐标（备用方法）
    :param start_cell: 起始单元格，如 "A1"
    :param end_cell: 结束单元格，如 "C5"
    :return: (start_col, start_row, end_col, end_row)
    """
    def cell_to_indices(cell):
        """将单元格坐标转换为行列索引"""
        import re
        match = re.match(r'([A-Z]+)(\d+)', cell.upper())
        if not match:
            raise ValueError(f"无效的单元格坐标: {cell}")

        col_letters, row_num = match.groups()

        # 将列字母转换为数字
        col_num = 0
        for char in col_letters:
            col_num = col_num * 26 + (ord(char) - ord('A') + 1)

        return col_num - 1, int(row_num) - 1  # 转换为0基索引

    start_col, start_row = cell_to_indices(start_cell)
    end_col, end_row = cell_to_indices(end_cell)

    return start_col, start_row, end_col, end_row


def export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path):
    """
    从Excel文件中导出指定区域为图片
    :param excel_path: Excel文件路径
    :param sheet_name: Sheet名称
    :param area_str: 所选区域，如 "A2:C8" 或 "(A2,C8)"
    :param output_image_path: 输出图片路径
    :return: 图片保存路径
    """
    try:
        print(f"📖 正在读取Excel文件: {excel_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"📍 区域: {area_str}")

        # 检查文件是否存在
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name=sheet_name, header=None)

        # 解析区域字符串
        start_col, start_row, end_col, end_row = parse_area_string(area_str)

        print(f"📊 解析区域: 列{start_col+1}-{end_col+1}, 行{start_row+1}-{end_row+1}")

        # 提取指定区域的数据
        region_data = df.iloc[start_row:end_row+1, start_col:end_col+1]

        # 转换为字符串并处理NaN值
        region_data = region_data.fillna('').astype(str)

        print(f"📏 数据尺寸: {region_data.shape[0]}行 x {region_data.shape[1]}列")

        # 生成图片
        image = create_table_image(region_data)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")

        print(f"✅ 图片已保存: {output_image_path}")
        print(f"📏 图片尺寸: {image.size[0]}x{image.size[1]}")

        return output_image_path

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        raise


def create_table_image(data, font_size=12, cell_padding=10):
    """
    根据数据创建表格图片
    :param data: pandas DataFrame
    :param font_size: 字体大小
    :param cell_padding: 单元格内边距
    :return: PIL Image对象
    """
    rows, cols = data.shape

    if rows == 0 or cols == 0:
        raise ValueError("数据为空，无法生成图片")

    # 获取字体
    font = get_chinese_font(font_size)

    # 计算每列的最大宽度
    col_widths = []
    for col in range(cols):
        max_width = 0
        for row in range(rows):
            cell_value = str(data.iloc[row, col])
            if font:
                try:
                    bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), cell_value, font=font)
                    text_width = bbox[2] - bbox[0]
                except:
                    text_width = len(cell_value) * font_size // 2
            else:
                text_width = len(cell_value) * 8
            max_width = max(max_width, text_width)
        col_widths.append(max_width + cell_padding * 2)

    # 计算行高
    row_height = font_size + cell_padding * 2

    # 计算图片总尺寸
    img_width = sum(col_widths)
    img_height = rows * row_height

    # 创建图片
    image = Image.new('RGB', (img_width, img_height), 'white')
    draw = ImageDraw.Draw(image)

    # 绘制表格
    current_y = 0
    for row in range(rows):
        current_x = 0
        for col in range(cols):
            cell_value = str(data.iloc[row, col])
            cell_width = col_widths[col]

            # 绘制单元格边框
            draw.rectangle([current_x, current_y, current_x + cell_width, current_y + row_height],
                         outline='black', width=1)

            # 绘制文本
            if cell_value and cell_value.strip():
                text_x = current_x + cell_padding
                text_y = current_y + cell_padding

                try:
                    draw.text((text_x, text_y), cell_value, fill='black', font=font)
                except Exception as e:
                    print(f"⚠️ 绘制文本失败: {e}, 文本: {cell_value}")
                    # 尝试不使用字体绘制
                    try:
                        draw.text((text_x, text_y), cell_value, fill='black')
                    except:
                        pass

            current_x += cell_width
        current_y += row_height

    return image


def main():
    """示例用法"""
    # 示例调用
    excel_path = "2015.xlsx"  # 替换为实际的Excel文件路径
    sheet_name = "2015年接待记录"     # 替换为实际的工作表名称
    area_str = "A1:C5"        # 替换为实际的区域
    output_path = f"导出图片_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

    try:
        result_path = export_sheet_as_image(excel_path, sheet_name, area_str, output_path)
        print(f"\n🎉 导出成功！图片路径: {result_path}")
    except Exception as e:
        print(f"\n❌ 导出失败: {e}")


if __name__ == "__main__":
    main()
