#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台依赖安装脚本
根据操作系统自动安装所需的Python库
"""

import platform
import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    system_os = platform.system()
    print(f"🖥️  检测到操作系统: {system_os}")
    
    # 通用依赖
    common_packages = ["pillow"]
    
    # 平台特定依赖
    if system_os == "Windows":
        platform_packages = ["keyboard"]
        print("📦 Windows平台，将安装keyboard库")
    else:
        platform_packages = ["pynput"]
        print(f"📦 {system_os}平台，将安装pynput库")
    
    # 安装所有包
    all_packages = common_packages + platform_packages
    
    print(f"\n🚀 开始安装依赖包: {', '.join(all_packages)}")
    
    success_count = 0
    for package in all_packages:
        print(f"\n📥 正在安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
            success_count += 1
        else:
            print(f"❌ {package} 安装失败")
    
    print(f"\n📊 安装结果: {success_count}/{len(all_packages)} 个包安装成功")
    
    if success_count == len(all_packages):
        print("🎉 所有依赖安装完成！")
        print("\n📋 系统特定依赖提醒:")
        if system_os == "Linux":
            print("   Linux还需要安装: sudo apt-get install xclip")
        elif system_os == "Darwin":
            print("   macOS还需要安装: brew install pngpaste")
        
        print("\n🚀 现在可以运行程序了:")
        print("   python excel_screenshot_hotkey.py")
    else:
        print("⚠️  部分依赖安装失败，请手动安装")

if __name__ == "__main__":
    main()
