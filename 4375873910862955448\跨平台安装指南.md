# Excel表格截图工具 - 跨平台安装指南

## 🖥️ 支持的操作系统

- ✅ **Windows** - 完全支持（包括COM接口）
- ✅ **Linux** - 基本支持（不支持COM接口）
- ✅ **macOS** - 基本支持（不支持COM接口）

## 📦 依赖安装

### 通用依赖（所有平台）

```bash
pip install pillow tkinter
```

### Windows 特有依赖

```bash
# Windows COM接口支持
pip install pywin32

# 热键支持（可选）
pip install keyboard
```

### Linux 特有依赖

```bash
# 系统依赖
sudo apt-get update
sudo apt-get install python3-tk xclip

# Python依赖
pip install pillow

# 热键支持（可选，需要root权限）
pip install keyboard
```

### macOS 特有依赖

```bash
# 系统依赖
brew install pngpaste

# Python依赖
pip install pillow

# 热键支持（可选）
pip install keyboard
```

## 🔧 功能对比

| 功能 | Windows | Linux | macOS |
|------|---------|-------|-------|
| 手动截图 | ✅ | ✅ | ✅ |
| 剪贴板图片 | ✅ | ✅* | ✅* |
| COM接口 | ✅ | ❌ | ❌ |
| 热键支持 | ✅ | ✅** | ✅ |

*需要安装额外工具（xclip/pngpaste）
**可能需要root权限

## 📋 使用方式

### Windows 用户（推荐）

1. **COM接口方式**（最智能）：
   - 在WPS/Excel中选择区域
   - 程序自动截取

2. **剪贴板图片方式**：
   - 在WPS/Excel中右键"复制为图片"
   - 程序获取剪贴板图片

3. **手动截图方式**：
   - 手动选择屏幕区域

### Linux 用户

1. **剪贴板图片方式**（推荐）：
   - 在LibreOffice Calc中复制为图片
   - 确保安装了xclip

2. **手动截图方式**：
   - 手动选择屏幕区域

### macOS 用户

1. **剪贴板图片方式**（推荐）：
   - 在Numbers/Excel中复制为图片
   - 确保安装了pngpaste

2. **手动截图方式**：
   - 手动选择屏幕区域

## ⚠️ 注意事项

### Linux 系统
- COM接口不可用（Windows专有技术）
- 需要安装xclip支持剪贴板图片
- 热键功能可能需要root权限
- 建议使用LibreOffice Calc替代Excel

### macOS 系统
- COM接口不可用
- 需要安装pngpaste支持剪贴板图片
- 可以使用Numbers或Excel for Mac

### 跨平台兼容性
- 手动截图方式在所有平台都可用
- 剪贴板图片方式需要额外工具支持
- COM接口仅限Windows系统

## 🚀 快速开始

1. **安装依赖**（根据您的系统）
2. **运行程序**：
   ```bash
   python excel_to_image.py
   ```
3. **选择适合您系统的方式**
4. **开始截图**

## 🔍 故障排除

### Linux 常见问题
```bash
# 如果提示xclip未找到
sudo apt-get install xclip

# 如果热键不工作
sudo python excel_to_image.py
```

### macOS 常见问题
```bash
# 如果提示pngpaste未找到
brew install pngpaste

# 如果权限问题
sudo python excel_to_image.py
```

### Windows 常见问题
```bash
# 如果COM接口不工作
pip install pywin32

# 重新注册COM组件
python -c "import win32com.client; print('COM可用')"
```
